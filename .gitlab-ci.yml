services:
      - docker:dind
default:
  tags: [docker]
stages:
  - test
  - sonarqube-check
  - build
  - deploy
  
include:
  - template: Jobs/SAST.gitlab-ci.yml

semgrep-sast:
  stage: test
  rules:
    - if: $CI_PIPELINE_SOURCE == 'merge_request_event' && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == $CI_DEFAULT_BRANCH
  when: always
  allow_failure: true

before_script:
  - export PROJECT_VESRION=${CI_COMMIT_TIMESTAMP}-${CI_COMMIT_SHORT_SHA}


sonarqube-check:
  stage: sonarqube-check
  image: 
    name: sonarsource/sonar-scanner-cli:5.0
    entrypoint: [""]
  variables:
    SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar"  # Defines the location of the analysis task cache
    GIT_DEPTH: "0"  # Tells git to fetch all the branches of the project, required by the analysis task
  cache:
    key: "${CI_JOB_NAME}"
    paths:
      - .sonar/cache
  script: 
    - sonar-scanner -Dsonar.projectVersion=$PROJECT_VESRION
  allow_failure: true
  rules:
    - if: $CI_PIPELINE_SOURCE == 'merge_request_event' && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == $CI_DEFAULT_BRANCH

Build Push Staging:
  tags:
    - build
    - dind
  stage: build
  image: docker:28-rc-cli
  rules:
    - if: $CI_COMMIT_BRANCH != $CI_DEFAULT_BRANCH
      when: always  # Make each job manual to control when it runs
  script:
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
    # cache-from here tells a tagged image to be used as a cache source
    - >
      docker build
      --build-arg PORT=$port
      --build-arg ENV_FILE=ansible/inventories/staging/.env
      --no-cache
      --tag $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA
      .
    - docker tag $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA $CI_REGISTRY_IMAGE:latest
    - docker push $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA
    - docker push $CI_REGISTRY_IMAGE:latest
  environment:
    name: staging

Deploy Staging:
  image: registry.gitlab.com/sportion/shared/images/default-runner-image:master
  stage: deploy
  script:
    - apk add openssh
    - apk add ansible
    - eval $(ssh-agent -s)
    - echo "$DEPLOYMENT_SSH_KEY_STG" | ssh-add -
    - chmod -R 700 ansible
    - cd ansible
    - ansible-playbook playbooks/deploy.yml -i ./inventories/staging/inventory -l twilio_web_connector -f 5 -u ansible  -e DOCKER_IMAGE=$CI_REGISTRY_IMAGE:$CI_COMMIT_SHA
  rules:
    - if: $CI_COMMIT_BRANCH != $CI_DEFAULT_BRANCH
      when: always  # Make each job manual to control when it runs
  dependencies:
    - Build Push Staging
  resource_group: staging_deployment
  tags:
    - TW
    - atlas
    - docker
    - lower
  environment:
    name: staging


Build Push Prod:
  tags:
    - build
    - dind
  stage: build
  image: docker:28-rc-cli
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: always  # Make each job manual to control when it runs
  script:
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
    # cache-from here tells a tagged image to be used as a cache source
    - >
      docker build
      --build-arg PORT=$port
      --build-arg ENV_FILE=ansible/inventories/production/.env
      --no-cache
      --tag $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA
      .
    - docker tag $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA $CI_REGISTRY_IMAGE:latest
    - docker push $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA
    - docker push $CI_REGISTRY_IMAGE:latest
  environment:
    name: production

Deploy Prod:
  image: registry.gitlab.com/sportion/shared/images/default-runner-image:master
  stage: deploy
  script:
    - apk add openssh
    - apk add ansible
    - eval $(ssh-agent -s)
    - echo "$DEPLOYMENT_SSH_KEY_STG" | ssh-add -
    - chmod -R 700 ansible
    - cd ansible
    - ansible-playbook playbooks/deploy.yml -i ./inventories/production/inventory -l twilio_web_connector -f 5 -u ansible  -e DOCKER_IMAGE=$CI_REGISTRY_IMAGE:$CI_COMMIT_SHA
    
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: manual  # Make each job manual to control when it runs
  dependencies:
    - Build Push Prod
  resource_group: production_deployment
  tags:
    - TW
    - atlas
    - docker
    - prod
  environment:
    name: production
