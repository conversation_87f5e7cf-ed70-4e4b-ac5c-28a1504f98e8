{"name": "twilio-webchat-widget", "version": "1.0.0", "description": "", "private": true, "main": "server/index.js", "license": "MIT", "dependencies": {"@emotion/core": "^10.0.28", "@sendgrid/mail": "7.7.0", "@twilio-paste/core": "^10.20.0", "@twilio-paste/icons": "^6.1.0", "@twilio-paste/theme": "^5.3.3", "@twilio/conversations": "2.1.0-rc.0", "@types/file-saver": "2.0.5", "file-saver": "2.0.5", "google-auth-library": "8.5.1", "googleapis": "107.0.0", "googleapis-common": "6.0.3", "jszip": "3.10.1", "lodash.merge": "^4.6.2", "lodash.throttle": "^4.1.1", "loglevel": "^1.7.1", "mime-types": "^2.1.34", "react": "^17.0.2", "react-app-rewired": "^2.2.1", "react-dom": "^17.0.2", "react-redux": "^7.2.6", "react-scripts": "^5.0.0", "redux": "^4.1.2", "redux-thunk": "^2.4.0", "slugify": "1.6.5"}, "devDependencies": {"@testing-library/jest-dom": "^5.15.1", "@testing-library/react": "^12.1.2", "@types/jest": "^27.0.3", "@types/lodash.merge": "^4.6.6", "@types/lodash.throttle": "^4.1.6", "@types/mime-types": "^2.1.1", "@types/react": "^17.0.20", "@types/react-dom": "^17.0.9", "@typescript-eslint/eslint-plugin": "^5.3.1", "@typescript-eslint/parser": "^5.4.0", "axios": "^0.24.0", "cors": "^2.8.5", "cypress": "^9.3.1", "cypress-file-upload": "^5.0.8", "dotenv": "^16.0.0", "eslint": "^7.11.0", "eslint-config-prettier": "^7.2.0", "eslint-config-twilio-react": "^1.35.1", "eslint-config-twilio-ts": "^1.35.1", "eslint-plugin-prettier": "^4.0.0", "express": "4.17.2", "fetch-mock-jest": "^1.5.1", "jsonwebtoken": "^8.5.1", "node-fetch": "^2.6.6", "nodemon": "^2.0.15", "prettier": "^2.4.1", "twilio": "^3.74.0", "typescript": "^4.4.2", "webpack-bundle-analyzer": "^4.5.0"}, "scripts": {"start": "node server/index.js", "dev": "nodemon server/index.js", "server": "nodemon server/index.js", "server:ci": "node server/index.js"}, "browserslist": [">0.2%", "not dead", "not ie <= 11", "not op_mini all"], "engines": {"node": ">=14"}}