require("dotenv").config();
const express = require("express");

const { validateRequestOriginMiddleware } = require("./middlewares/validateRequestOriginMiddleware");
const { initWebchatController } = require("./controllers/initWebchatController");
const { refreshTokenController } = require("./controllers/refreshTokenController");
const { emailTranscriptController } = require("./controllers/emailTranscriptController");
const asyncWrapper = require('./utils/async-wrapper');
const { BBardController } = require('./controllers/BBardController');

const { generateVoiceToken, initWebcallController } = require('./controllers/voiceTokenController');
const { generateToken, createVideoTask } =  require('./controllers/videoTokenController');

const cors = require("cors");
const { allowedOrigins } = require("./helpers/getAllowedOrigins");

const app = express();
const port = process.env.PORT || 3011;

app.use(express.json());
app.use(
    cors({
        origin: allowedOrigins
    })
);
app.listen(port, () => {
    console.log(`Twilio Webchat App server running on port ${port}`);
});

app.get('/connector/v3/health', (req, res) => {
  res.status(200).send('Twilio Webchat App server running OK');
})

app.post("/connector/v3/initWebchat", validateRequestOriginMiddleware, initWebchatController);
app.post("/connector/v3/initWebcall", validateRequestOriginMiddleware, initWebcallController);
app.post("/connector/v3/refreshToken", validateRequestOriginMiddleware, refreshTokenController);
app.post("/connector/v3/email", validateRequestOriginMiddleware, emailTranscriptController);
app.post("/connector/v3/token", validateRequestOriginMiddleware, generateToken);
app.post("/connector/v3/createVideoTask", validateRequestOriginMiddleware, createVideoTask);
