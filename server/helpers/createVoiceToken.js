const Twilio = require("twilio");
const { TOKEN_TTL_IN_SECONDS } = require("../constants");
const { logInterimAction } = require("./logs");

// Creates a Twilio Voice Access Token (JWT)
// Requires env vars: ACCOUNT_SID, API_KEY, API_SECRET, APP_SID
const createVoiceToken = (identity, env) => {
  logInterimAction("Creating new voice token");
  const { AccessToken } = Twilio.jwt;
  const { VoiceGrant } = AccessToken;

  const voiceGrant = new VoiceGrant({
    outgoingApplicationSid: env.APP_SID,
    incomingAllow: true
  });

  const token = new AccessToken(
    process.env.ACCOUNT_SID,
    env.API_KEY,
    env.API_SECRET,
    {
      identity,
      ttl: TOKEN_TTL_IN_SECONDS
    }
  );

  token.addGrant(voiceGrant);
  const jwt = token.toJwt();
  logInterimAction("New voice token created");
  return jwt;
};

module.exports = { createVoiceToken };

