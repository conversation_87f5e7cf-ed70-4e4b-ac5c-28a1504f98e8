import jwt from "jsonwebtoken";
import fs from "fs";
import path from "path";
import { fileURLToPath } from 'url';
const __filename = fileURLToPath(import.meta.url);

const __dirname = path.dirname(__filename);


const privateKEY = fs.readFileSync(
    path.join(__dirname, "../../config/iacavaawf02.key"),
    "utf8"
);
const publicKEY = fs.readFileSync(
    path.join(__dirname, "../../config/iacavaawf02.pem"),
    "utf8"
);

class JWT {
    static verifyToken(token) {
        const verifyOptions = {
            expiresIn: parseInt(process.env.expiresIn, 10),
            issuer: process.env.issuer,
            subject: process.env.subject,
            algorithm: process.env.algorithm,
        };
        console.log(verifyOptions);
        return jwt.verify(token, publicKEY, verifyOptions);
    }

    static issueToken(customerIdentifier) {
        const payload = {
            customerIdentifier,
            accountId: process.env.accountId,
        };
        const signingOptions = {
            expiresIn: parseInt(process.env.expiresIn, 10),
            issuer: process.env.issuer,
            subject: process.env.subject,
            algorithm: process.env.algorithm,
        };
        const newToken = jwt.sign(
            payload,
            {
                key: privateKEY,
                passphrase: process.env.secret,
            },
            signingOptions
        );
        console.log("token", newToken);
        return newToken;
    }
}

export default JWT;
