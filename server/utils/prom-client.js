import client from 'prom-client';
let responsetimesumm = ''
class PromClient {
    static startMetrics() {
        const collectDefaultMetrics = client.collectDefaultMetrics;
        collectDefaultMetrics();
        responsetimesumm = new client.Summary ({
            name: 'chatapp_response_time_summary',
            help: 'Latency in percentiles',
            });
    }

    static observeResTime(time){
        responsetimesumm.observe(time);
    }

}

export default PromClient;