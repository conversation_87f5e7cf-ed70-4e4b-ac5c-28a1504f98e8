let logger;
try {
  const winston = require('winston');

  const transports = [];
  // Always include console transport to avoid silent failures
  transports.push(new winston.transports.Console({ level: process.env.debug === 'true' ? 'debug' : 'info' }));

  // File transports (may fail in restricted environments). Paths can be overridden via env.
  const errFile = process.env.LOG_ERROR_FILE || '/var/log/chat_connector/error.log';
  const combinedFile = process.env.LOG_COMBINED_FILE || '/var/log/chat_connector/combined.log';
  try {
    transports.push(new winston.transports.File({ level: 'error', filename: errFile }));
    if (process.env.debug === 'true') {
      transports.push(new winston.transports.File({ filename: combinedFile }));
    }
  } catch (_) {
    // Ignore file transport errors; console transport remains
  }

  logger = winston.createLogger({
    level: 'info',
    format: winston.format.combine(
      winston.format.timestamp(),
      winston.format.json()
    ),
    transports
  });
} catch (e) {
  // Fallback to a minimal console-based logger if winston is unavailable
  logger = {
    info: (...args) => console.info(...args),
    error: (...args) => console.error(...args),
    warn: (...args) => console.warn(...args),
    debug: (...args) => { if (process.env.debug === 'true') console.debug(...args); },
    log: (entry) => {
      try {
        const { level = 'info', message = '', ...meta } = entry || {};
        const out = console[level] || console.log;
        out(message, meta);
      } catch (_) {
        console.log(entry);
      }
    }
  };
}

module.exports = logger;
