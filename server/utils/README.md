This folder contains utility libraries used by the server.

Expected contents:
- libs/wasm_crypt.js
- libs/wasm_crypt_bg.js (or wasm_crypt_bg.wasm)

Place the wasm crypt artifacts here (under server/utils/libs/). The AvayaController expects:
- server/utils/libs/wasm_crypt.js exporting ClientCrypt
- server/utils/libs/wasm_crypt_bg.js exporting an init function, and a wasm binary file wasm_crypt_bg.wasm

If your actual file names or import shapes differ, update server/controllers/AvayaController.js accordingly.

