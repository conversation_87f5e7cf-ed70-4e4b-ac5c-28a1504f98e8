class AppError extends Error {
    constructor(error, message, data = null, isOperational = true) {
      super();
      if (typeof (error) === 'object') {
        this.statusCode = error.statusCode || 500;
        this.message = error.message || null;
        this.data = error.data || null;
        this.isOperational = error.isOperational || null;
      } else {
        this.statusCode = error;
        this.message = message;
        this.data = data;
        this.isOperational = isOperational;
      }
    }
  }
  
export default AppError;