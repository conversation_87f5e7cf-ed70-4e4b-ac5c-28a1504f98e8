const Twilio = require("twilio");

const { AccessToken } = Twilio.jwt;
const { VideoGrant } = AccessToken;

const createVideoTask = async (req, res) => {
  try {
    const ip = (
      req.headers["x-forwarded-for"]?.split(",")[0] ||
      req.socket.remoteAddress
    )?.replace(/^::ffff:/, "");
    console.log(req.headers["x-forwarded-for"]);
    console.log(ip);

    const attributes = JSON.stringify({
      type: "video",
      ip,
      ...req.body,
    });

    const ACCOUNT_SID = req.body.msw ? process.env.MSW_ACCOUNT_SID : process.env.ACCOUNT_SID;
    const AUTH_TOKEN = req.body.msw ? process.env.MSW_AUTH_TOKEN : process.env.AUTH_TOKEN;
    const WORKSPACE_SID = req.body.msw ? process.env.MSW_WORKSPACE_SID : process.env.WORKSPACE_SID;
    const WORKFLOW_SID = req.body.msw
      ? JSON.parse(process.env.VIDEO_CALL_CONFIGURATIONS)[req.body.product].MSW_WORKFLOW_SID
      : process.env.WORKFLOW_SID;
    const TASK_CHANNEL_SID = req.body.msw ? process.env.MSW_TASK_CHANNEL_SID : process.env.TASK_CHANNEL_SID;

    const client = Twilio(ACCOUNT_SID, AUTH_TOKEN);

    try {
      const task = await client.taskrouter.v1
        .workspaces(WORKSPACE_SID)
        .tasks.create({
          workflowSid: WORKFLOW_SID,
          taskChannel: TASK_CHANNEL_SID,
          attributes: attributes,
        });

      return res.json({ taskSid: task.sid, attributes: task.attributes });
    } catch (twilioError) {
      console.error("Twilio Task creation error:", twilioError);
      return res.status(500).json({ error: twilioError.message });
    }
  } catch (error) {
    console.error("Error in createVideoTask:", error);
    return res.status(500).json({ error: error.message });
  }
};

const generateToken = async (req, res) => {
  try {
    const { identity, roomName, msw, product } = req.body;
    if (!identity) {
      return res.status(400).json({ error: "Identity is required" });
    }

    const ACCOUNT_SID = msw ? process.env.MSW_ACCOUNT_SID : process.env.ACCOUNT_SID;
    const API_KEY = msw
      ? JSON.parse(process.env.VIDEO_CALL_CONFIGURATIONS)[product].MSW_API_KEY
      : process.env.API_KEY;
    const API_SECRET = msw
      ? JSON.parse(process.env.VIDEO_CALL_CONFIGURATIONS)[product].MSW_API_SECRET
      : process.env.API_SECRET;
    const AUTH_TOKEN = msw ? process.env.MSW_AUTH_TOKEN : process.env.AUTH_TOKEN;

    if (!ACCOUNT_SID || !API_KEY || !API_SECRET) {
      return res.status(500).json({ error: "Twilio credentials are not configured" });
    }

    const client = Twilio(ACCOUNT_SID, AUTH_TOKEN);
    const token = new AccessToken(ACCOUNT_SID, API_KEY, API_SECRET, {
      identity,
      ttl: 3600, // 1 hour expiry
    });

    let room;
    try {
      room = await client.video.rooms.create({
        uniqueName: `video_${identity}_${Date.now()}`,
        type: "group",
      });
    } catch (roomError) {
      console.error("Error creating Twilio Video room:", roomError);
      return res.status(500).json({ error: roomError.message });
    }

    const videoGrant = new VideoGrant({ room: roomName ?? room.uniqueName });
    token.addGrant(videoGrant);

    return res.json({
      token: token.toJwt(),
      roomSid: room?.sid,
      roomName: room?.uniqueName,
    });
  } catch (error) {
    console.error("Error in generateToken:", error);
    return res.status(500).json({ error: error.message });
  }
};

module.exports = { generateToken, createVideoTask };

