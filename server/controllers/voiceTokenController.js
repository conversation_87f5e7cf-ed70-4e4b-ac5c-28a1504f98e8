const axios = require('axios');
const { createVoiceToken } = require('../helpers/createVoiceToken');
const { logInitialAction, logFinalAction, logInterimAction } = require('../helpers/logs');
const { version } = require('../../package.json');
const { BBardController } = require('./BBardController');
const { TOKEN_TTL_IN_SECONDS } = require('../constants');

// POST /api/twilio/voice-token
// Body: { identity: string }
const generateVoiceToken = async (req, res) => {
  logInitialAction('Generating Twilio Voice token');

  try {
    const identity = req.body?.identity || 'voice-user';

    // Check if AUDIO_CALL_CONFIGURATIONS exists and is valid JSON
    if (!process.env.AUDIO_CALL_CONFIGURATIONS) {
      logInterimAction('Missing AUDIO_CALL_CONFIGURATIONS environment variable');
      return res.status(500).json({ error: 'Server not configured for Voice.' });
    }

    let audioCallConfigs;
    try {
      audioCallConfigs = JSON.parse(process.env.AUDIO_CALL_CONFIGURATIONS);
    } catch (e) {
      logInterimAction('Invalid AUDIO_CALL_CONFIGURATIONS JSON:', e.message);
      return res.status(500).json({ error: 'Server configuration error.' });
    }

    const env = audioCallConfigs[req.body?.formData?.language || "IN"];
    if (!env.APP_SID) {
      logInterimAction('Missing APP_SID for '+req.body?.formData?.language);
      return res.status(500).json({ error: 'Server not configured for Voice.' });
    }

    const token = createVoiceToken(identity, env);
    logFinalAction('Voice token generated');
    return res.json({ token });
  } catch (e) {
    logInterimAction('Failed to generate Voice token:', e?.message || e);
    return res.status(500).json({ error: 'Failed to generate Voice token' });
  }
};

// POST /v3/initWebcall
// Similar to initWebchat but for voice calls - creates a conversation and returns voice token + conversationSid
const initWebcallController = async (req, res) => {
  logInitialAction('Initiating webcall');

  try {
    const customerFriendlyName = req.body?.formData?.friendlyName || 'Voice Customer';
    const identity = req.body?.identity || `voice-${Date.now()}`;

    // Check if AUDIO_CALL_CONFIGURATIONS exists and is valid JSON
    if (!process.env.AUDIO_CALL_CONFIGURATIONS) {
      logInterimAction('Missing AUDIO_CALL_CONFIGURATIONS environment variable');
      return res.status(500).json({ error: 'Server not configured for Voice calls.' });
    }

    let audioCallConfigs;
    try {
      audioCallConfigs = JSON.parse(process.env.AUDIO_CALL_CONFIGURATIONS);
    } catch (e) {
      logInterimAction('Invalid AUDIO_CALL_CONFIGURATIONS JSON:', e.message);
      return res.status(500).json({ error: 'Server configuration error.' });
    }

    const env = audioCallConfigs[req.body?.formData?.language || "IN"];

    if (!env.APP_SID) {
      logInterimAction('Missing APP_SID for'+req.body?.formData?.language);
      return res.status(500).json({ error: 'Server not configured for Voice.' });
    }

    const engagementParameters = buildEngagementParameters(req);
    const engagementQuery = buildEngagementQuery(engagementParameters);
    const webhookUrl = `${env.TWIML_WEBHOOK_URL}?${engagementQuery}`;
    // Create a voice call via Twilio Voice API to get a CallSid
    let callSid;
    try {
      logInterimAction('Creating voice call');
      const params = new URLSearchParams();
      params.append("To", req.body?.formData?.phoneNumber || 'client:' + identity); // Can be phone number or client identity
      params.append("From", process.env.TWILIO_PHONE_NUMBER || 'client:system'); // Your Twilio phone number or client identity
      params.append("Url", webhookUrl);
      params.append("ApplicationSid", env.APP_SID); // TwiML Application SID for handling the call
      params.append("StatusCallback", process.env.VOICE_STATUS_CALLBACK_URL || ''); // Optional: webhook for call status updates
      params.append("StatusCallbackEvent", 'initiated,ringing,answered,completed'); // Call events to track
      params.append("Record", 'false'); // Set to 'true' if you want to record the call
      params.append("Timeout", '30'); // Ring timeout in seconds

      // Add engagement parameters
      for (const [key, value] of Object.entries(engagementParameters)) {
        if (value !== undefined && value !== null) {
          params.append(key, String(value));
        }
      }

      const callRes = await axios.post(
        `https://api.twilio.com/2010-04-01/Accounts/${process.env.ACCOUNT_SID}/Calls.json`,
        params,
        {
          auth: {
            username: env.API_KEY,
            password: env.API_SECRET
          },
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
          }
        }
      );
      console.log('Call created:', callRes.data);
      callSid = callRes.data.sid;
      logInterimAction('Voice call created:', callSid);
    } catch (e) {
      logInterimAction('Failed to create voice call:', e.response?.data?.message || e.message);
      return res.status(500).json({ error: 'Failed to create voice call' });
    }

    // Generate voice token
    const token = createVoiceToken(identity, env);

    // Attempt to fetch bbc_id and arion_route from BBardController Conveyor Belt API
    let bbcId = null;
    let arionRoute = null;
    try {
      const conveyorBeltResult = await BBardController.ConveyorBeltCreateCrypt(engagementParameters);
      arionRoute = conveyorBeltResult?.data?.arion_route || null;
      bbcId = conveyorBeltResult?.data?.bbc_id || null;
    } catch (e) {
      logInterimAction("ConveyorBeltCreateCrypt failed:", e?.message || e);
    }

    logFinalAction('Webcall successfully initiated');
    return res.json({
      token,
      callSid,
      identity,
      expiration: Date.now() + TOKEN_TTL_IN_SECONDS * 1000,
      bbc_id: bbcId,
      arion_route: arionRoute
    });
  } catch (e) {
    logInterimAction('Failed to initiate webcall:', e?.message || e);
    return res.status(500).json({ error: 'Failed to initiate webcall' });
  }
};

function buildEngagementQuery(engagementParameters) {
  const q = new URLSearchParams();
  for (const [key, value] of Object.entries(engagementParameters)) {
    if (value !== undefined && value !== null)
      q.append(key, String(value));
  }
  return q.toString();
}

function buildEngagementParameters(request) {
  const form = request.body?.formData || {};

  return {
    skillset: form.skillset,
    username: form.username || form.Brand,
    name: form.name || form.friendlyName || "Customer",
    email: form.email,
    phoneNumber: form.phoneNumber,
    Brand: form.Brand,
    product: form.product,
    inquiry: form.inquiry,
    subinquiry: typeof form.subinquiry !== "undefined" ? form.subinquiry : "",
    deposit: typeof form.deposit !== "undefined" ? Number(form.deposit) : 0,
    IP: request.headers["x-forwarded-for"] || request.socket?.remoteAddress || "",
    country: form.country,
    language: form.language,
    referer: request.get("referer") || "",
    RefererPlatform: form.RefererPlatform,
    CustomerVerified: String(form.CustomerVerified || "false"),
    CustomerSegment: form.CustomerSegment,
    SuggestedPM: form.SuggestedPM,
    OtherPM: form.OtherPM,
    Restart: String(form.Restart || "false"),
    source: form.source || ""
  };
}

module.exports = { generateVoiceToken, initWebcallController };

