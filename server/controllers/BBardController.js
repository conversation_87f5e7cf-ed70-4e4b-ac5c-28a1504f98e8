const axios = require('axios');
const path = require('path');

const { logInterimAction } = require('../helpers/logs');
const StatusCodes = require('../utils/http-status-codes');
const logger = require('../utils/logger');

let crypt;
let ClientCrypt;
let __wbg_init;
let usedEngagementIds = new Set();


async function loadWasmCrypt() {
  if (!ClientCrypt || !__wbg_init) {
    const { ClientCrypt: C, __wbg_init: I } = require('../utils/libs/wasm_crypt.js');
    ClientCrypt = C;
    __wbg_init = I;
  }
}

class BBardController {
  static isEngagementUsed(id) {
    return usedEngagementIds.has(id);
  }

  static setEngagementUsed(id) {
    usedEngagementIds.add(id);
  }

  static async MaskEmail(email) {
    try {
      if (!email) return email;
      const [local, domain] = String(email).split('@');
      if (!domain) return email;
      if (local.length <= 2) return `*${local.slice(-1)}@${domain}`;
      const masked = `${local[0]}${'*'.repeat(local.length - 2)}${local.slice(-1)}`;
      return `${masked}@${domain}`;
    } catch (e) {
      return email;
    }
  }
  static async FetchCrypt() {
    try {
      if (!crypt) {
        await loadWasmCrypt();
        await __wbg_init(path.join(__dirname, '../utils/libs/wasm_crypt_bg.wasm'));
        const res = await axios.get('https://arion-crypt-server-001-futdin2xua-an.a.run.app/get-key', { responseType: 'text' });
        const text = res.data;
        crypt = ClientCrypt.new(text);
      }
      return crypt;
    } catch (error) {
      throw new Error(error?.message || 'Crypt init error');
    }
  }

  static async ConveyorBeltCreateCrypt(engagementParameters) {
    const fetchedCrypt = await BBardController.FetchCrypt();
 
    const data = {
      channel_type: 'chat',
      skillset: engagementParameters.skillset,
      username: engagementParameters.username !== '' ? engagementParameters.username : engagementParameters.Brand,
      name: engagementParameters.name,
      email: engagementParameters.email,
      phone_number: engagementParameters.phoneNumber,
      brand: engagementParameters.Brand,
      product: engagementParameters.product,
      inquiry: engagementParameters.inquiry,
      subinquiry: typeof engagementParameters.subinquiry !== 'undefined' ? engagementParameters.subinquiry : '',
      transaction_amount: typeof engagementParameters.deposit !== 'undefined' ? Number(engagementParameters.deposit) : 0,
      ip: engagementParameters.IP,
      country: engagementParameters.country,
      language: engagementParameters.language,
      source: '',
      referer: engagementParameters.referer,
      referer_platform: engagementParameters.RefererPlatform,
      customer_verified: JSON.parse(String(engagementParameters.CustomerVerified).toLowerCase()),
      customer_segment: engagementParameters.CustomerSegment,
      suggested_pm: engagementParameters.SuggestedPM,
      other_pm: engagementParameters.OtherPM,
      restart: JSON.parse(String(engagementParameters.Restart).toLowerCase())
    };
    
    const encrypted_request = fetchedCrypt.encrypt(JSON.stringify(data));

    const base = process.env.baseballAPIEndPoint || '';
    const baseNoSlash = base.replace(/\/+$/, '');
    const baseWithProxy = baseNoSlash.endsWith('/proxy') ? baseNoSlash : `${baseNoSlash}/proxy`;
    const url = `${baseWithProxy}/ccaas/v1/context-conv`;

    logInterimAction('ConveyorBeltCreateCrypt POST URL:', url);

    let n_text;
    try {
      const response = await axios.post(
        url,
        encrypted_request,
        { headers: { 'Content-Type': 'text/plain' }, responseType: 'text' }
      );
      n_text = response.data;
    } catch (e) {
      let decoded = '';
      try {
        if (e.response?.data) {
          decoded = Buffer.from(String(e.response.data), 'base64').toString('utf8');
        }
      } catch (_) {}
      logInterimAction('ConveyorBeltCreateCrypt HTTP error', {
        status: e.response?.status,
        url: e.config?.url,
        data: e.response?.data,
        decoded
      });
      throw e;
    }

    const result = JSON.parse(fetchedCrypt.decrypt(n_text));
    logInterimAction('ConveyorBeltCreateCrypt result:', result);
    return result;
  }

  static async BaseBallCreateCryptRequest(req, res) {
    try {
      const { contextParameters } = req.body || {};
      if (!contextParameters) {
        return res.status(StatusCodes.CLIENT.BAD_REQUEST).send({ hasError: 1, message: 'Missing contextParameters' });
      }

      const engagementId = contextParameters.engagement_id;
      if (!engagementId) {
        return res.status(StatusCodes.CLIENT.BAD_REQUEST).send({ hasError: 1, message: 'Missing engagement_id' });
      }

      if (BBardController.isEngagementUsed(engagementId)) {
        logInterimAction(`Duplicate engagement_id: ${engagementId}`);
        return res.status(StatusCodes.CLIENT.BAD_REQUEST).send({ hasError: 1, message: 'Duplicate engagement_id' });
      }

      // Mark as used before async operations to avoid race
      BBardController.setEngagementUsed(engagementId);

      const fetchedCrypt = await BBardController.FetchCrypt();
      const maskedEmail = await BBardController.MaskEmail(contextParameters.email, contextParameters.Brand);
      contextParameters.email = maskedEmail;

      const data = {
        channel_type: 'chat',
        engagement_id: contextParameters.engagement_id,
        bbc_id: contextParameters.bbc_id,
        skillset: contextParameters.skillset,
        username: contextParameters.username !== '' ? contextParameters.username : contextParameters.Brand,
        name: contextParameters.name,
        email: contextParameters.email,
        phone_number: contextParameters.phoneNumber,
        brand: contextParameters.Brand,
        product: contextParameters.product,
        inquiry: contextParameters.inquiry,
        subinquiry: typeof contextParameters.subinquiry !== 'undefined' ? contextParameters.subinquiry : '',
        transaction_amount: typeof contextParameters.deposit !== 'undefined' ? Number(contextParameters.deposit) : 0,
        ip: contextParameters.IP,
        country: contextParameters.country,
        language: contextParameters.language,
        source: '',
        referer: contextParameters.referer,
        referer_platform: contextParameters.RefererPlatform,
        customer_verified: JSON.parse(String(contextParameters.CustomerVerified).toLowerCase()),
        customer_segment: contextParameters.CustomerSegment,
        suggested_pm: '',
        other_pm: '',
        restart: false
      };

      // optional heavy logging per request; using error level to match your example
      try { logger.log({ level: 'error', bbc_data: data }); } catch (_) {}

      const encrypted_request = fetchedCrypt.encrypt(JSON.stringify(data));

      // Build URL ensuring exactly one /proxy
      const base = process.env.baseballAPIEndPoint || '';
      const baseNoSlash = base.replace(/\/+$/, '');
      const baseWithProxy = baseNoSlash.endsWith('/proxy') ? baseNoSlash : `${baseNoSlash}/proxy`;
      const url = `${baseWithProxy}/ccaas/v1/start-conv`;

      let n_text;
      try {
        const response = await axios.post(
          url,
          encrypted_request,
          { headers: { 'Content-Type': 'text/plain' }, responseType: 'text' }
        );
        n_text = response.data;
      } catch (e) {
        let decoded = '';
        try {
          if (e.response?.data) {
            decoded = Buffer.from(String(e.response.data), 'base64').toString('utf8');
          }
        } catch (_) {}
        try { logger.log({ level: 'error', message: 'Error while BaseBallCreateCryptRequest', meta: { err: e, decoded } }); } catch (_) {}
        let status = StatusCodes.SERVER.INTERNAL;
        if (e.response && e.response.status) status = e.response.status;
        else if (e.statusCode) status = e.statusCode;
        return res.status(status).send({ hasError: 1, message: decoded || e.message || 'Upstream error' });
      }

      const result = JSON.parse(fetchedCrypt.decrypt(n_text));
      return res.status(StatusCodes.SERVER.SUCCESS).send({ hasError: 0, encrypt: n_text, result });
    } catch (e) {
      try { logger.log({ level: 'error', message: 'Error while BaseBallCreateCryptRequest', meta: { err: e } }); } catch (_) {}
      const status = e.statusCode || StatusCodes.SERVER.INTERNAL;
      return res.status(status).send({ hasError: 1, message: e.message || 'Unexpected error' });
    }
  }
}

module.exports = { BBardController };

