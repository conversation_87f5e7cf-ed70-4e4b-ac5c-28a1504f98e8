const axios = require("axios");
const { createToken } = require("../helpers/createToken");
const { TOKEN_TTL_IN_SECONDS } = require("../constants");
const { getTwilioClient } = require("../helpers/getTwilioClient");
const { logFinalAction, logInitialAction, logInterimAction } = require("../helpers/logs");
const { version } = require('./../../package.json');
const { BBardController } = require("./BBardController");

const contactWebchatOrchestrator = async (request, arionRoute, customerFriendlyName) => {
    logInterimAction("Calling Webchat Orchestrator");

    const params = new URLSearchParams();
    params.append("AddressSid", process.env.ADDRESS_SID);
    params.append("ChatFriendlyName", "Webchat widget");
    params.append("CustomerFriendlyName", customerFriendlyName);
    params.append(
        "PreEngagementData",
        JSON.stringify({
            ...request.body?.formData,
            "arion_route":arionRoute,
            friendlyName: customerFriendlyName
        })
    );

    let conversationSid;
    let identity;

    try {
        const res = await axios.post(`https://flex-api.twilio.com/v2/WebChats`, params, {
            auth: {
                username: process.env.ACCOUNT_SID,
                password: process.env.AUTH_TOKEN
            },
            headers: {
                "ui-version": version
            }
        });
        ({ identity, conversation_sid: conversationSid } = res.data);
    } catch (e) {
        logInterimAction("Something went wrong during the orchestration:", e.response?.data?.message);
        throw e.response.data;
    }

    logInterimAction("Webchat Orchestrator successfully called");

    return {
        conversationSid,
        identity
    };
};

const sendUserMessage = (conversationSid, identity, messageBody) => {
    logInterimAction("Sending user message");
    return getTwilioClient()
        .conversations.conversations(conversationSid)
        .messages.create({
            body: messageBody,
            author: identity,
            xTwilioWebhookEnabled: true // trigger webhook
        })
        .then(() => {
            logInterimAction(`(async) User message sent: ${conversationSid} :  ${identity} :  ${messageBody}`);
        })
        .catch((e) => {
            logInterimAction(`(async) Couldn't send user message: ${e?.message}`);
        });
};

const sendWelcomeMessage = (conversationSid, customerFriendlyName) => {
    logInterimAction("Sending welcome message");
    /**return getTwilioClient()
        .conversations.conversations(conversationSid)
        .messages.create({
            body: `Welcome ${customerFriendlyName}! An agent will be with you in just a moment.`,
            author: "Concierge"
        })
        .then(() => {
            logInterimAction("(async) Welcome message sent");
        })
        .catch((e) => {
            logInterimAction(`(async) Couldn't send welcome message: ${e?.message}`);
        });**/
};

const initWebchatController = async (request, response) => {
    logInitialAction("Initiating webchat");

    // Attempt to fetch bbc_id and arion_route from BBardController Conveyor Belt API
    let bbcId = null;
    let arionRoute = null;
    try {
        
        const engagementParameters = buildEngagementParameters(request);
        
        const conveyorBeltResult = await BBardController.ConveyorBeltCreateCrypt(engagementParameters);
        
        arionRoute = conveyorBeltResult?.data?.arion_route || null;
        bbcId = conveyorBeltResult?.data?.bbc_id || null;
        
    } catch (e) {
        logInterimAction("ConveyorBeltCreateCrypt failed:", e?.message || e);
    }

    const customerFriendlyName = request.body?.formData?.friendlyName || "Customer";

    let conversationSid;
    let identity;

    // Hit Webchat Orchestration endpoint to generate conversation and get customer participant sid
    try {
        const result = await contactWebchatOrchestrator(request, arionRoute, customerFriendlyName);
        ({ identity, conversationSid } = result);
    } catch (error) {
        return response.status(500).send(`Couldn't initiate WebChat: ${error?.message}`);
    }

    // Generate token for customer
    const token = createToken(identity);

    // OPTIONAL — if user query is defined
    if (request.body?.formData?.query) {
        // use it to send a message in behalf of the user with the query as body
        sendUserMessage(conversationSid, identity, request.body.formData.query).then(() =>
            // and then send another message from Concierge, letting the user know that an agent will help them soon
            sendWelcomeMessage(conversationSid, customerFriendlyName)
        );
    }

    response.send({
        token,
        conversationSid,
        expiration: Date.now() + TOKEN_TTL_IN_SECONDS * 1000,
        bbc_id: bbcId,
        arion_route: arionRoute
    });

    logFinalAction("Webchat successfully initiated");
};

function buildEngagementParameters(request) {
    const form = request.body?.formData || {};
    
    return {
        skillset: form.skillset,
        username: form.username || form.Brand,
        name: form.name || form.friendlyName || "Customer",
        email: form.email,
        phoneNumber: form.phoneNumber,
        Brand: form.Brand,
        product: form.product,
        inquiry: form.inquiry,
        subinquiry: typeof form.subinquiry !== "undefined" ? form.subinquiry : "",
        deposit: typeof form.deposit !== "undefined" ? Number(form.deposit) : 0,
        IP: request.headers["x-forwarded-for"] || request.socket?.remoteAddress || "",
        country: form.country,
        language: form.language,
        referer: request.get("referer") || "",
        RefererPlatform: form.RefererPlatform,
        CustomerVerified: String(form.CustomerVerified || "false"),
        CustomerSegment: form.CustomerSegment,
        SuggestedPM: form.SuggestedPM,
        OtherPM: form.OtherPM,
        Restart: String(form.Restart || "false"),
        source: form.source || ""
    };
}

module.exports = { initWebchatController };
