---
- name: Deploy
  hosts: twilio_web_connector
  gather_facts: true
  become: true
 

  tasks:

    - name: Execute docker login
      ansible.builtin.command:
        cmd: docker login -u "{{ username }}" -p "{{ password }}" "{{ registry }}"
      vars:
        registry: registry.gitlab.com
        username: "{{ lookup('env','CI_REGISTRY_USER') }}"
        password: "{{ lookup('env','CI_REGISTRY_PASSWORD') }}"
      become: yes  # If Docker requires sudo

    
    - name: Pull the private Docker image
      ansible.builtin.command:
        cmd: docker pull {{ DOCKER_IMAGE }}
      become: yes  # If Docker requires sudo

    - name: Stop running container
      ansible.builtin.command:
        cmd: docker stop twilio_web_connector
      register: run_result
      ignore_errors: yes 
      # changed_when: "'Error' in run_result.stderr" 

    - name: Run twilio_web_connector container
      ansible.builtin.command:
        cmd: docker run -d  --rm --name twilio_web_connector -p 443:3010 -p 80:3010 {{ DOCKER_IMAGE }}
      register: run_result
      changed_when: "'already in use' not in run_result.stderr"  # Detect if container was newly created
      failed_when: "'Error' in run_result.stderr"  # Fail on Docker errors

    - name: Docker cleanup - prune unused images
      ansible.builtin.command:
        cmd: docker image prune -a -f
      register: run_result
      ignore_errors: yes 
      failed_when: "'Error' in run_result.stderr"  # Fail on Docker errors 
      changed_when: "'Total reclaimed space' in run_result.stdout"  # Detect if any images were pruned
    
    - name: Perform Docker logout
      ansible.builtin.command:
        cmd: docker logout {{ registry_url | default(omit) }}
      vars:
        registry_url: "registry.gitlab.com"  # Optionally specify your registry
      register: logout_result
      changed_when: "'Removing login credentials' in logout_result.stdout"  # Detect if creds were removed
      failed_when: "'Error' in logout_result.stderr and 'not logged in' not in logout_result.stdout"  # Handle errors
      ignore_errors: yes  # Optional: Continue even if not logged in