# Copy this file to .env and fill in values
# Server
PORT=3010
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3010
TWILIO_REGION=us1

# Twilio credentials
ACCOUNT_SID=
AUTH_TOKEN=
API_KEY=
API_SECRET=
CONVERSATIONS_SERVICE_SID=
ADDRESS_SID=

# Token/crypto
# API_SECRET should also be used as JWT signing secret in refreshTokenController

# SendGrid (for email transcript)
SENDGRID_API_KEY=
FROM_EMAIL=

# External endpoints
baseballAPIEndPoint=

# Feature flags
EMAIL_TRANSCRIPT_ENABLED=false
DOWNLOAD_TRANSCRIPT_ENABLED=false

